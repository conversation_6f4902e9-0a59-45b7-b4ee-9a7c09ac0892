"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import ShitParticle from "./shit-particle";

interface Particle {
  id: number;
  x: number; // viewport width percentage
  y: number; // viewport height percentage
  size: number;
  opacity: number;
  velocityY: number;
  rotation: number;
}

const ShitParticleBackground: React.FC = () => {
  const [particles, setParticles] = useState<Particle[]>([]);
  const animationFrameId = useRef<number | null>(null);
  const nextParticleId = useRef(0);
  const lastUpdateTime = useRef(0);

  const createParticle = useCallback(() => {
    const newParticle: Particle = {
      id: nextParticleId.current++,
      x: Math.random() * 100, // Random X across viewport width
      y: -5, // Start slightly above the viewport
      size: Math.random() * 20 + 15, // Size between 15 and 35px
      opacity: 1,
      velocityY: Math.random() * 0.5 + 0.2, // Slow downward movement
      rotation: Math.random() * 360,
    };
    return newParticle;
  }, []);

  const animateParticles = useCallback((currentTime: DOMHighResTimeStamp) => {
    const deltaTime = currentTime - lastUpdateTime.current;
    lastUpdateTime.current = currentTime;

    setParticles((prevParticles) => {
      const updatedParticles = prevParticles
        .map((p) => ({
          ...p,
          y: p.y + p.velocityY * (deltaTime / 16), // Adjust speed based on delta time
          opacity: p.opacity - 0.005 * (deltaTime / 16), // Fade out slowly
        }))
        .filter((p) => p.opacity > 0 && p.y < 105); // Remove if fully transparent or off-screen

      // Add new particles periodically
      if (Math.random() < 0.05 && updatedParticles.length < 50) { // Control density
        updatedParticles.push(createParticle());
      }

      return updatedParticles;
    });

    animationFrameId.current = requestAnimationFrame(animateParticles);
  }, [createParticle]);

  useEffect(() => {
    lastUpdateTime.current = performance.now();
    animationFrameId.current = requestAnimationFrame(animateParticles);

    // Initial particles
    for (let i = 0; i < 10; i++) {
      setParticles((prev) => [...prev, createParticle()]);
    }

    return () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, [animateParticles, createParticle]);

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {particles.map((p) => (
        <ShitParticle
          key={p.id}
          x={p.x}
          y={p.y}
          size={p.size}
          opacity={p.opacity}
          rotation={p.rotation}
        />
      ))}
    </div>
  );
};

export default ShitParticleBackground;