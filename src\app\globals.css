@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode (shit-like) */
    --background: 30 25% 95%; /* Light beige/brown */
    --foreground: 30 25% 15%; /* Dark brown */
    --card: 30 25% 98%; /* Slightly lighter card */
    --card-foreground: 30 25% 15%;
    --popover: 30 25% 98%;
    --popover-foreground: 30 25% 15%;
    --primary: 30 50% 30%; /* Medium brown */
    --primary-foreground: 30 25% 98%; /* Off-white */
    --secondary: 40 40% 85%; /* Yellowish brown */
    --secondary-foreground: 30 25% 15%;
    --muted: 30 20% 90%; /* Muted beige */
    --muted-foreground: 30 15% 40%; /* Muted dark brown */
    --accent: 60 30% 70%; /* Greenish brown */
    --accent-foreground: 30 25% 15%;
    --destructive: 0 84.2% 60.2%; /* Keep red for destructive */
    --destructive-foreground: 0 0% 98%;
    --border: 30 10% 80%; /* Lighter brown border */
    --input: 30 10% 80%;
    --ring: 30 50% 30%;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    /* Dark mode (shit-like) */
    --background: 30 25% 10%; /* Dark brown */
    --foreground: 30 25% 90%; /* Light beige */
    --card: 30 25% 12%;
    --card-foreground: 30 25% 90%;
    --popover: 30 25% 12%;
    --popover-foreground: 30 25% 90%;
    --primary: 30 50% 60%; /* Lighter brown */
    --primary-foreground: 30 25% 10%;
    --secondary: 40 40% 30%; /* Darker yellowish brown */
    --secondary-foreground: 30 25% 90%;
    --muted: 30 20% 20%;
    --muted-foreground: 30 15% 60%;
    --accent: 60 30% 40%;
    --accent-foreground: 30 25% 90%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 30 10% 20%;
    --input: 30 10% 20%;
    --ring: 30 50% 60%;

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}