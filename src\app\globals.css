@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 30 20% 95%; /* Light beige/off-white */
    --foreground: 30 20% 15%; /* Dark brown */

    --card: 30 20% 98%;
    --card-foreground: 30 20% 15%;

    --popover: 30 20% 98%;
    --popover-foreground: 30 20% 15%;

    --primary: 30 40% 30%; /* Medium brown */
    --primary-foreground: 30 20% 98%;

    --secondary: 30 20% 85%; /* Light brown/tan */
    --secondary-foreground: 30 20% 15%;

    --muted: 30 20% 85%;
    --muted-foreground: 30 10% 40%; /* Grayish brown */

    --accent: 30 20% 85%;
    --accent-foreground: 30 20% 15%;

    --destructive: 0 84.2% 60.2%; /* Keep red for destructive */
    --destructive-foreground: 0 0% 98%;

    --border: 30 15% 70%;
    --input: 30 15% 70%;
    --ring: 30 40% 30%;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    --radius: 0.5rem;

    --sidebar-background: 30 20% 90%; /* Lighter brown for sidebar */
    --sidebar-foreground: 30 20% 15%;
    --sidebar-primary: 30 40% 30%;
    --sidebar-primary-foreground: 30 20% 98%;
    --sidebar-accent: 30 20% 80%;
    --sidebar-accent-foreground: 30 20% 15%;
    --sidebar-border: 30 15% 70%;
    --sidebar-ring: 30 40% 30%;
  }

  .dark {
    --background: 30 20% 10%; /* Dark brown */
    --foreground: 30 20% 90%; /* Light beige */

    --card: 30 20% 12%;
    --card-foreground: 30 20% 90%;

    --popover: 30 20% 12%;
    --popover-foreground: 30 20% 90%;

    --primary: 30 40% 60%; /* Lighter brown */
    --primary-foreground: 30 20% 10%;

    --secondary: 30 20% 25%; /* Medium dark brown */
    --secondary-foreground: 30 20% 90%;

    --muted: 30 20% 25%;
    --muted-foreground: 30 10% 60%;

    --accent: 30 20% 25%;
    --accent-foreground: 30 20% 90%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 30 15% 30%;
    --input: 30 15% 30%;
    --ring: 30 40% 60%;

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --sidebar-background: 30 20% 15%; /* Darker brown for sidebar */
    --sidebar-foreground: 30 20% 90%;
    --sidebar-primary: 30 40% 60%;
    --sidebar-primary-foreground: 30 20% 10%;
    --sidebar-accent: 30 20% 20%;
    --sidebar-accent-foreground: 30 20% 90%;
    --sidebar-border: 30 15% 30%;
    --sidebar-ring: 30 40% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}