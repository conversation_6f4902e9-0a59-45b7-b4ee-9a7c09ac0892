"use client";

import React, { useEffect, useState } from "react";

interface ParticleData {
  id: number;
  left: number;
  top: number;
  delay: number;
  duration: number;
  size: number;
}

const CSSParticleBackground = () => {
  const [particles, setParticles] = useState<ParticleData[]>([]);

  useEffect(() => {
    // Generate particles on client side to avoid hydration mismatch
    const newParticles = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      left: Math.random() * 100,
      top: Math.random() * 100,
      delay: Math.random() * 5,
      duration: 3 + Math.random() * 3,
      size: 24 + Math.random() * 16,
    }));
    setParticles(newParticles);
  }, []);

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {particles.map((particle) => (
        <div
          key={particle.id}
          className="absolute animate-float pointer-events-none select-none opacity-60 hover:opacity-100 transition-opacity"
          style={{
            left: `${particle.left}%`,
            top: `${particle.top}%`,
            animationDelay: `${particle.delay}s`,
            animationDuration: `${particle.duration}s`,
            fontSize: `${particle.size}px`,
          }}
        >
          💩
        </div>
      ))}
    </div>
  );
};

export default CSSParticleBackground;
