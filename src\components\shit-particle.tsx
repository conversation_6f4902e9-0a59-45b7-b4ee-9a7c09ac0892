"use client";

import React from "react";
import { cn } from "@/lib/utils";

interface ShitParticleProps {
  x: number;
  y: number;
  size: number;
  opacity: number;
  rotation: number;
}

const ShitParticle: React.FC<ShitParticleProps> = ({ x, y, size, opacity, rotation }) => {
  return (
    <div
      className={cn(
        "absolute pointer-events-none select-none will-change-transform"
      )}
      style={{
        left: `${x}vw`,
        top: `${y}vh`,
        fontSize: `${size}px`,
        opacity: opacity,
        transform: `rotate(${rotation}deg)`,
        transition: "opacity 0.1s linear", // Smooth fade out
      }}
    >
      💩
    </div>
  );
};

export default ShitParticle;